"""
HTTP client for ThinkBuddy API
"""
import httpx
import logging
import traceback
import json
import os
from datetime import datetime
from typing import Dict, AsyncGenerator
from models import ThinkBuddyRequest, ThinkBuddyResponse
from token_manager import TokenManager

logger = logging.getLogger(__name__)


def save_crash_log(operation: str, error: Exception, context: Dict = None, request_data: Dict = None, response_data: Dict = None):
    """Save detailed crash information to a file in /crash directory"""
    try:
        # Create crash directory if it doesn't exist
        crash_dir = "crash"
        os.makedirs(crash_dir, exist_ok=True)

        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        filename = f"crash_{timestamp}_{operation.replace(' ', '_').replace('/', '_')}.json"
        filepath = os.path.join(crash_dir, filename)

        # Gather comprehensive error information
        crash_info = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "error": {
                "type": type(error).__name__,
                "message": str(error),
                "args": list(error.args) if hasattr(error, 'args') else [],
            },
            "traceback": traceback.format_exc(),
            "context": context or {},
            "request_data": request_data or {},
            "response_data": response_data or {},
        }

        # Add HTTP-specific error details if available
        if hasattr(error, 'response'):
            try:
                crash_info["error"]["http_status"] = error.response.status_code
                crash_info["error"]["http_headers"] = dict(error.response.headers)
                crash_info["error"]["http_text"] = error.response.text
                crash_info["error"]["http_url"] = str(error.response.url)
            except Exception:
                pass

        # Add request-specific error details if available
        if hasattr(error, 'request'):
            try:
                crash_info["error"]["request_method"] = error.request.method
                crash_info["error"]["request_url"] = str(error.request.url)
                crash_info["error"]["request_headers"] = dict(error.request.headers)
            except Exception:
                pass

        # Sanitize sensitive data
        if "authorization" in crash_info.get("request_data", {}):
            crash_info["request_data"]["authorization"] = "[REDACTED]"
        if "apikey" in crash_info.get("context", {}):
            crash_info["context"]["apikey"] = "[REDACTED]"

        # Write crash log to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(crash_info, f, indent=2, ensure_ascii=False)

        logger.error(f"Crash log saved to: {filepath}")
        return filepath

    except Exception as save_error:
        logger.error(f"Failed to save crash log: {save_error}")
        return None


class ThinkBuddyClient:
    def __init__(self, api_url: str, token_manager: TokenManager):
        self.api_url = api_url.rstrip('/')
        self.token_manager = token_manager
        self.client = httpx.AsyncClient(timeout=60.0)

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for ThinkBuddy API requests"""
        # Get API key from environment variable
        api_key = os.getenv("THINKBUDDY_API_KEY")
        if not api_key:
            raise ValueError("THINKBUDDY_API_KEY environment variable is required")

        headers = {
            "accept": "*/*",
            "accept-language": "en-US,en;q=0.9",
            "apikey": api_key,
            "authorization": self.token_manager.get_authorization_header(),
            "content-type": "application/json",
            "priority": "u=1, i",
            "referer": "https://thinkbuddy.ai/",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "x-client-info": "supabase-js-web/2.49.1"
        }
        return headers

    async def chat_completion(self, request: ThinkBuddyRequest) -> ThinkBuddyResponse:
        """
        Send a non-streaming chat completion request to ThinkBuddy
        """
        try:
            headers = self._get_headers()
            url = f"{self.api_url}/functions/v1/completions"

            # Convert request to dict and ensure stream is False
            request_data = request.model_dump()
            request_data["stream"] = False

            logger.info(f"Sending request to ThinkBuddy: {url}")
            logger.debug(f"Request data: {request_data}")

            response = await self.client.post(
                url,
                headers=headers,
                json=request_data,
                follow_redirects=True
            )

            response.raise_for_status()
            response_data = response.json()

            logger.debug(f"ThinkBuddy response: {response_data}")

            # Parse response into ThinkBuddyResponse model
            return ThinkBuddyResponse(**response_data)

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error from ThinkBuddy: {e.response.status_code} - {e.response.text}")
            save_crash_log(
                operation="chat_completion_http_error",
                error=e,
                context={"url": url, "status_code": e.response.status_code},
                request_data=request_data,
                response_data={"status_code": e.response.status_code, "text": e.response.text}
            )
            raise
        except httpx.RequestError as e:
            logger.error(f"Request error to ThinkBuddy: {e}")
            save_crash_log(
                operation="chat_completion_request_error",
                error=e,
                context={"url": url},
                request_data=request_data
            )
            raise
        except Exception as e:
            logger.error(f"Unexpected error calling ThinkBuddy: {e}")
            save_crash_log(
                operation="chat_completion_unexpected_error",
                error=e,
                context={"url": url},
                request_data=request_data
            )
            raise

    async def chat_completion_stream(self, request: ThinkBuddyRequest) -> AsyncGenerator[str, None]:
        """
        Send a streaming chat completion request to ThinkBuddy with enhanced error handling
        """
        headers = self._get_headers()
        url = f"{self.api_url}/functions/v1/completions"

        # Convert request to dict and ensure stream is True
        request_data = request.model_dump()
        request_data["stream"] = True

        logger.info(f"Sending streaming request to ThinkBuddy: {url}")
        logger.debug(f"Request data: {request_data}")

        try:
            async with self.client.stream(
                "POST",
                url,
                headers=headers,
                json=request_data,
                follow_redirects=True
            ) as response:
                try:
                    response.raise_for_status()
                except httpx.HTTPStatusError as e:
                    logger.error(f"HTTP error from ThinkBuddy streaming: {e.response.status_code}")
                    save_crash_log(
                        operation="chat_completion_stream_http_error",
                        error=e,
                        context={"url": url, "status_code": e.response.status_code},
                        request_data=request_data,
                        response_data={"status_code": e.response.status_code, "text": getattr(e.response, 'text', 'N/A')}
                    )
                    raise

                try:
                    async for line in response.aiter_lines():
                        if line.strip():
                            # ThinkBuddy should return SSE format: "data: {json}"
                            if line.startswith("data: "):
                                data_part = line[6:]  # Remove "data: " prefix
                                if data_part.strip() == "[DONE]":
                                    break
                                yield data_part
                            else:
                                # If not in expected format, yield as-is
                                yield line

                except Exception as stream_error:
                    # Handle streaming response content access errors
                    error_str = str(stream_error)
                    if "attempted to access streaming response content" in error_str.lower():
                        logger.warning(f"Streaming response content access error: {error_str}")
                        save_crash_log(
                            operation="streaming_response_content_access_error",
                            error=stream_error,
                            context={"url": url, "error_type": "streaming_content_access"},
                            request_data=request_data
                        )
                        # This is likely a rate limiting or API issue, re-raise to trigger retry
                        raise httpx.RequestError(f"Streaming response access failed: {error_str}")
                    else:
                        # Other streaming errors
                        logger.error(f"Error during stream processing: {stream_error}")
                        save_crash_log(
                            operation="chat_completion_stream_processing_error",
                            error=stream_error,
                            context={"url": url},
                            request_data=request_data
                        )
                        raise

        except httpx.RequestError as e:
            error_str = str(e)
            logger.error(f"Request error to ThinkBuddy streaming: {error_str}")

            # Enhanced error context for DNS resolution failures
            context = {"url": url}
            if "temporary failure in name resolution" in error_str.lower():
                context["error_type"] = "dns_resolution_failure"
                context["retry_recommended"] = True
            elif "connection error" in error_str.lower():
                context["error_type"] = "connection_error"
                context["retry_recommended"] = True

            save_crash_log(
                operation="chat_completion_stream_request_error",
                error=e,
                context=context,
                request_data=request_data
            )
            raise
        except Exception as e:
            logger.error(f"Unexpected error calling ThinkBuddy streaming: {e}")
            save_crash_log(
                operation="chat_completion_stream_unexpected_error",
                error=e,
                context={"url": url},
                request_data=request_data
            )
            raise

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
