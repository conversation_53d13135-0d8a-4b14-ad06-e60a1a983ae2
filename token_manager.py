"""
Token manager for automatic ThinkBuddy token refresh
"""
import asyncio
import httpx
import logging
import time
import os
import traceback
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


def save_crash_log(operation: str, error: Exception, context: dict = None, request_data: dict = None):
    """Save detailed crash information to a file in /crash directory"""
    try:
        # Create crash directory if it doesn't exist
        crash_dir = "crash"
        os.makedirs(crash_dir, exist_ok=True)

        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        filename = f"crash_{timestamp}_{operation.replace(' ', '_').replace('/', '_')}.json"
        filepath = os.path.join(crash_dir, filename)

        # Gather comprehensive error information
        crash_info = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "error": {
                "type": type(error).__name__,
                "message": str(error),
                "args": list(error.args) if hasattr(error, 'args') else [],
            },
            "traceback": traceback.format_exc(),
            "context": context or {},
            "request_data": request_data or {},
        }

        # Add HTTP-specific error details if available
        if hasattr(error, 'response'):
            try:
                crash_info["error"]["http_status"] = error.response.status_code
                crash_info["error"]["http_headers"] = dict(error.response.headers)
                crash_info["error"]["http_text"] = error.response.text
                crash_info["error"]["http_url"] = str(error.response.url)
            except Exception:
                pass

        # Sanitize sensitive data
        if request_data:
            sanitized_request = request_data.copy()
            if "refresh_token" in sanitized_request:
                sanitized_request["refresh_token"] = "[REDACTED]"
            if "access_token" in sanitized_request:
                sanitized_request["access_token"] = "[REDACTED]"
            crash_info["request_data"] = sanitized_request

        # Write crash log to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(crash_info, f, indent=2, ensure_ascii=False)

        logger.error(f"Crash log saved to: {filepath}")
        return filepath

    except Exception as save_error:
        logger.error(f"Failed to save crash log: {save_error}")
        return None


@dataclass
class TokenData:
    """Container for token information"""
    access_token: str
    refresh_token: str
    expires_at: Optional[int] = None
    token_type: str = "bearer"


class TokenManager:
    """Manages automatic token refresh for ThinkBuddy API"""

    def __init__(self, api_url: str, api_key: str, initial_access_token: str, initial_refresh_token: str):
        self.api_url = api_url.rstrip('/')
        self.api_key = api_key
        self.token_data = TokenData(
            access_token=initial_access_token,
            refresh_token=initial_refresh_token
        )
        self.client = httpx.AsyncClient(timeout=30.0)
        self.refresh_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()

        # User data from the token request example (keep consistent for all requests)
        # Generate current timestamp for dynamic fields
        current_time = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')

        self.user_data = {
            "id": "d1c42398-295a-449a-9b69-5d4e0d85ce3a",
            "aud": "authenticated",
            "role": "authenticated",
            "email": "<EMAIL>",
            "email_confirmed_at": "2025-03-18T17:15:11.913088Z",  # Keep original confirmation date
            "phone": "",
            "confirmed_at": "2025-03-18T17:15:11.913088Z",  # Keep original confirmation date
            "last_sign_in_at": current_time,  # Use current time for last sign in
            "app_metadata": {
                "provider": "google",
                "providers": ["google"]
            },
            "user_metadata": {
                "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocKJVyZP3gyR2_ZBtdoVJXQwvV1GPn6fNYh_nrfVWjbPorgtkEw=s96-c",
                "email": "<EMAIL>",
                "email_verified": True,
                "full_name": "Jonathan Noonan",
                "is_onboarded": True,
                "iss": "https://accounts.google.com",
                "name": "Jonathan Noonan",
                "phone_verified": False,
                "picture": "https://lh3.googleusercontent.com/a/ACg8ocKJVyZP3gyR2_ZBtdoVJXQwvV1GPn6fNYh_nrfVWjbPorgtkEw=s96-c",
                "provider_id": "108711733011680761102",
                "sub": "108711733011680761102"
            },
            "identities": [
                {
                    "identity_id": "15dd75fe-8303-4e30-82bf-61545ce98779",
                    "id": "108711733011680761102",
                    "user_id": "d1c42398-295a-449a-9b69-5d4e0d85ce3a",
                    "identity_data": {
                        "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocKJVyZP3gyR2_ZBtdoVJXQwvV1GPn6fNYh_nrfVWjbPorgtkEw=s96-c",
                        "email": "<EMAIL>",
                        "email_verified": True,
                        "full_name": "Jonathan Noonan",
                        "iss": "https://accounts.google.com",
                        "name": "Jonathan Noonan",
                        "phone_verified": False,
                        "picture": "https://lh3.googleusercontent.com/a/ACg8ocKJVyZP3gyR2_ZBtdoVJXQwvV1GPn6fNYh_nrfVWjbPorgtkEw=s96-c",
                        "provider_id": "108711733011680761102",
                        "sub": "108711733011680761102"
                    },
                    "provider": "google",
                    "last_sign_in_at": "2025-03-18T17:15:11.910128Z",  # Keep original sign in date for identity
                    "created_at": "2025-03-18T17:15:11.910181Z",  # Keep original creation date
                    "updated_at": current_time,  # Use current time for identity updated_at
                    "email": "<EMAIL>"
                }
            ],
            "created_at": "2025-03-18T17:15:11.907721Z",  # Keep original creation date
            "updated_at": current_time,  # Use current time for user updated_at
            "is_anonymous": False
        }

    async def refresh_token(self) -> bool:
        """Refresh the access token using the refresh token"""
        async with self._lock:
            try:
                url = f"{self.api_url}/auth/v1/token?grant_type=refresh_token"

                # Create request body with current token data and user info
                request_body = {
                    "access_token": self.token_data.access_token,
                    "token_type": self.token_data.token_type,
                    "expires_in": 3600,
                    "expires_at": int(time.time()) + 3600,
                    "refresh_token": self.token_data.refresh_token,
                    "user": self.user_data
                }

                # Headers including the required API key
                headers = {
                    "Content-Type": "application/json",
                    "apikey": self.api_key
                }

                logger.info("Refreshing ThinkBuddy token...")
                logger.debug(f"Token refresh URL: {url}")

                response = await self.client.post(
                    url,
                    json=request_body,
                    headers=headers
                )

                response.raise_for_status()
                response_data = response.json()

                # Update token data with new tokens
                old_access_token = self.token_data.access_token
                old_refresh_token = self.token_data.refresh_token

                self.token_data.access_token = response_data["access_token"]
                self.token_data.refresh_token = response_data["refresh_token"]
                self.token_data.expires_at = response_data.get("expires_at")

                logger.info("Successfully refreshed ThinkBuddy token")
                logger.debug(f"New token expires at: {self.token_data.expires_at}")

                # Update .env file with new tokens
                await self._update_env_file(old_access_token, old_refresh_token)

                return True

            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error refreshing token: {e.response.status_code} - {e.response.text}")
                save_crash_log(
                    operation="token_refresh_http_error",
                    error=e,
                    context={"url": url, "status_code": e.response.status_code},
                    request_data=request_body
                )
                return False
            except httpx.RequestError as e:
                logger.error(f"Request error refreshing token: {e}")
                save_crash_log(
                    operation="token_refresh_request_error",
                    error=e,
                    context={"url": url},
                    request_data=request_body
                )
                return False
            except Exception as e:
                logger.error(f"Unexpected error refreshing token: {e}")
                save_crash_log(
                    operation="token_refresh_unexpected_error",
                    error=e,
                    context={"url": url},
                    request_data=request_body
                )
                return False

    async def _update_env_file(self, old_access_token: str, old_refresh_token: str):
        """Update .env file with new tokens"""
        try:
            env_path = ".env"
            if not os.path.exists(env_path):
                logger.warning("No .env file found, skipping token update")
                return

            # Read current .env file
            with open(env_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Replace old tokens with new ones
            updated_content = content.replace(old_access_token, self.token_data.access_token)
            updated_content = updated_content.replace(old_refresh_token, self.token_data.refresh_token)

            # Write updated content back to .env file
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)

            logger.info("Successfully updated .env file with new tokens")

        except Exception as e:
            logger.error(f"Failed to update .env file: {e}")
            save_crash_log(
                operation="env_file_update_error",
                error=e,
                context={"env_path": env_path}
            )
            # Don't fail the token refresh if .env update fails

    async def start_auto_refresh(self):
        """Start the automatic token refresh task (every 40 minutes)"""
        if self.refresh_task and not self.refresh_task.done():
            logger.warning("Auto-refresh task already running")
            return

        logger.info("Starting automatic token refresh (every 40 minutes)")
        self.refresh_task = asyncio.create_task(self._refresh_loop())

    async def stop_auto_refresh(self):
        """Stop the automatic token refresh task"""
        if self.refresh_task and not self.refresh_task.done():
            logger.info("Stopping automatic token refresh")
            self.refresh_task.cancel()
            try:
                await self.refresh_task
            except asyncio.CancelledError:
                pass

    async def _refresh_loop(self):
        """Background task that refreshes tokens every 40 minutes"""
        while True:
            try:
                # Wait 40 minutes (2400 seconds)
                await asyncio.sleep(2400)

                # Refresh the token
                success = await self.refresh_token()
                if not success:
                    logger.error("Failed to refresh token, will retry in 40 minutes")

            except asyncio.CancelledError:
                logger.info("Token refresh loop cancelled")
                break
            except Exception as e:
                logger.error(f"Unexpected error in token refresh loop: {e}")
                save_crash_log(
                    operation="token_refresh_loop_error",
                    error=e,
                    context={"loop_iteration": True}
                )
                # Continue the loop even if there's an error
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    def get_current_access_token(self) -> str:
        """Get the current access token"""
        return self.token_data.access_token

    def get_authorization_header(self) -> str:
        """Get the authorization header value"""
        return f"Bearer {self.token_data.access_token}"

    async def close(self):
        """Clean up resources"""
        await self.stop_auto_refresh()
        await self.client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
