"""
OpenAI-compatible API server for ThinkBuddy
"""
import os
import logging
import traceback
import json
import asyncio
from datetime import datetime
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, HTTPException, Header, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from models import (
    ChatCompletionRequest, ModelsResponse, ModelInfo,
    ErrorResponse, ErrorDetail
)
from thinkbuddy_client import ThinkBuddyClient
from translator import APITranslator
from token_manager import TokenManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO").upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Global variables
thinkbuddy_client: ThinkBuddyClient = None
translator: APITranslator = None
token_manager: TokenManager = None
valid_api_keys = []


def save_crash_log(operation: str, error: Exception, context: dict = None, request_data: dict = None):
    """Save detailed crash information to a file in /crash directory"""
    try:
        # Create crash directory if it doesn't exist
        crash_dir = "crash"
        os.makedirs(crash_dir, exist_ok=True)

        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        filename = f"crash_{timestamp}_{operation.replace(' ', '_').replace('/', '_')}.json"
        filepath = os.path.join(crash_dir, filename)

        # Gather comprehensive error information
        crash_info = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "error": {
                "type": type(error).__name__,
                "message": str(error),
                "args": list(error.args) if hasattr(error, 'args') else [],
            },
            "traceback": traceback.format_exc(),
            "context": context or {},
            "request_data": request_data or {},
        }

        # Add HTTP-specific error details if available
        if hasattr(error, 'response'):
            try:
                crash_info["error"]["http_status"] = error.response.status_code
                crash_info["error"]["http_headers"] = dict(error.response.headers)
                crash_info["error"]["http_text"] = error.response.text
                crash_info["error"]["http_url"] = str(error.response.url)
            except Exception:
                pass

        # Add FastAPI HTTPException details
        if isinstance(error, HTTPException):
            crash_info["error"]["status_code"] = error.status_code
            crash_info["error"]["detail"] = error.detail

        # Sanitize sensitive data
        if request_data:
            sanitized_request = request_data.copy()
            if "authorization" in sanitized_request:
                sanitized_request["authorization"] = "[REDACTED]"
            if "x_api_key" in sanitized_request:
                sanitized_request["x_api_key"] = "[REDACTED]"
            crash_info["request_data"] = sanitized_request

        # Write crash log to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(crash_info, f, indent=2, ensure_ascii=False)

        logger.error(f"Crash log saved to: {filepath}")
        return filepath

    except Exception as save_error:
        logger.error(f"Failed to save crash log: {save_error}")
        return None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    global thinkbuddy_client, translator, token_manager

    # Startup
    logger.info("Starting OpenAI-compatible API server for ThinkBuddy")

    # Initialize token manager
    api_url = os.getenv("THINKBUDDY_API_URL", "https://api.thinkbuddy.ai")
    api_key = os.getenv("THINKBUDDY_API_KEY")
    access_token = os.getenv("THINKBUDDY_ACCESS_TOKEN")
    refresh_token = os.getenv("THINKBUDDY_REFRESH_TOKEN")

    if not access_token or not refresh_token:
        raise ValueError("THINKBUDDY_ACCESS_TOKEN and THINKBUDDY_REFRESH_TOKEN must be set")

    if not api_key:
        raise ValueError("THINKBUDDY_API_KEY must be set")

    token_manager = TokenManager(api_url, api_key, access_token, refresh_token)

    # Start automatic token refresh
    await token_manager.start_auto_refresh()

    # Initialize ThinkBuddy client with token manager
    thinkbuddy_client = ThinkBuddyClient(api_url, token_manager)

    # Initialize translator
    default_model = os.getenv("DEFAULT_MODEL", "claude-4-sonnet")
    translator = APITranslator(default_model)

    # Load API keys
    load_api_keys()

    logger.info(f"Initialized with ThinkBuddy API URL: {api_url}")
    logger.info(f"Default model mapping: {default_model}")
    logger.info("Automatic token refresh started (every 40 minutes)")

    yield

    # Shutdown
    logger.info("Shutting down...")
    if token_manager:
        await token_manager.close()
    if thinkbuddy_client:
        await thinkbuddy_client.close()


# Create FastAPI app
app = FastAPI(
    title="OpenAI-compatible API for ThinkBuddy",
    description="A proxy server that translates OpenAI API requests to ThinkBuddy API calls",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log incoming requests for debugging"""
    if request.url.path.startswith("/v1/"):
        logger.info(f"📥 {request.method} {request.url.path}")
        logger.debug(f"Headers: {dict(request.headers)}")

        # Log authorization headers specifically
        auth_header = request.headers.get("authorization")
        api_key_header = request.headers.get("x-api-key")
        if auth_header:
            logger.info(f"🔑 Authorization header present: {auth_header[:20]}...")
        if api_key_header:
            logger.info(f"🔑 X-API-Key header present: {api_key_header[:20]}...")

    response = await call_next(request)

    if request.url.path.startswith("/v1/") and response.status_code >= 400:
        logger.warning(f"❌ {request.method} {request.url.path} -> {response.status_code}")

    return response


def load_api_keys():
    """Load API keys from users file"""
    global valid_api_keys
    try:
        with open("users", "r") as f:
            valid_api_keys = []
            for line in f:
                key = line.strip()
                if key:
                    valid_api_keys.append(key)
        logger.info(f"Loaded {len(valid_api_keys)} API keys from users file")
    except FileNotFoundError:
        logger.error("users file not found - no API keys loaded")
        valid_api_keys = []
    except Exception as e:
        logger.error(f"Error loading API keys: {e}")
        valid_api_keys = []

def check_api_key(authorization: str = None, x_api_key: str = None):
    """Check if provided API key is valid"""
    api_key = None

    if authorization:
        if authorization.startswith("Bearer "):
            api_key = authorization[7:]
        else:
            api_key = authorization
    elif x_api_key:
        if x_api_key.startswith("Bearer "):
            api_key = x_api_key[7:]
        else:
            api_key = x_api_key

    if not api_key:
        create_error_response("No API key provided", "authentication_error", 401)

    # Check if the raw API key is valid
    if api_key not in valid_api_keys:
        create_error_response("Invalid API key", "authentication_error", 401)

    return api_key

def create_error_response(message: str, error_type: str = "invalid_request", status_code: int = 400):
    """Create a standardized error response"""
    error_detail = ErrorDetail(message=message, type=error_type)
    error_response = ErrorResponse(error=error_detail)
    raise HTTPException(status_code=status_code, detail=error_response.model_dump())





@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "openai-thinkbuddy-proxy"}


@app.get("/v1/models", response_model=ModelsResponse)
async def list_models():
    """List available models"""
    models = [
        ModelInfo(id="claude-4-sonnet"),
        # Add more models here as they become available:
        # ModelInfo(id="claude-3-opus"),
        # ModelInfo(id="claude-3-sonnet"),
        # ModelInfo(id="claude-3-haiku"),
    ]

    return ModelsResponse(data=models)


async def stream_chat_completion(request: ChatCompletionRequest) -> AsyncGenerator[str, None]:
    """Handle streaming chat completion with enhanced error handling"""
    chunk_id = None
    content_character_count = 0
    thinking_tag_sent = False

    async def _attempt_stream():
        nonlocal chunk_id, content_character_count

        # Translate request
        thinkbuddy_request = translator.openai_to_thinkbuddy(request)

        # Stream from ThinkBuddy
        async for chunk_data in thinkbuddy_client.chat_completion_stream(thinkbuddy_request):
            # Parse ThinkBuddy chunk
            chunk = translator.parse_thinkbuddy_stream_chunk(chunk_data)
            if not chunk:
                continue

            # Extract content
            content = translator.extract_content_from_chunk(chunk)
            if content:
                # Count actual content characters (excluding whitespace)
                content_character_count += len(content.strip())

                # Create OpenAI-compatible chunk
                if chunk_id is None:
                    chunk_id = f"chatcmpl-{chunk.get('id', 'unknown')}"

                openai_chunk = translator.create_stream_chunk(
                    content=content,
                    original_request=request,
                    chunk_id=chunk_id
                )
                yield openai_chunk

        # Send final [DONE] chunk
        yield translator.create_stream_done_chunk()

    try:
        # Try streaming with retry logic
        attempt = 0
        max_retries = 2
        base_delay = 4.0

        while attempt <= max_retries:
            try:
                # If this is a retry after sending <thinking>, send closing tag first
                if thinking_tag_sent and attempt > 0:
                    close_thinking_chunk = translator.create_stream_chunk(
                        content="</thinking>",
                        original_request=request,
                        chunk_id=chunk_id or f"chatcmpl-retry-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                    )
                    yield close_thinking_chunk
                    thinking_tag_sent = False

                async for chunk in _attempt_stream():
                    yield chunk
                break  # Success, exit retry loop

            except Exception as e:
                attempt += 1
                error_str = str(e).lower()

                # Check if it's a retryable error and we haven't exceeded max retries
                if attempt <= max_retries and any(retryable in error_str for retryable in [
                    "temporary failure in name resolution",
                    "connection error",
                    "timeout",
                    "network",
                    "dns",
                    "streaming response content"
                ]):
                    # Only retry if we have less than 2 characters of actual content
                    if content_character_count < 2:
                        delay = base_delay * (2 ** (attempt - 1))
                        logger.warning(f"Retryable streaming error on attempt {attempt}/{max_retries + 1}: {e}. Retrying in {delay}s...")

                        # Send <thinking> tag if this is the first error
                        if not thinking_tag_sent:
                            thinking_chunk = translator.create_stream_chunk(
                                content="<thinking>",
                                original_request=request,
                                chunk_id=chunk_id or f"chatcmpl-thinking-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                            )
                            yield thinking_chunk
                            thinking_tag_sent = True

                        await asyncio.sleep(delay)
                        continue
                    else:
                        # Already streaming significant content, just end cleanly
                        logger.warning(f"Error after streaming {content_character_count} characters, ending stream cleanly")
                        save_crash_log(
                            operation="streaming_chat_completion_mid_stream",
                            error=e,
                            context={
                                "model": request.model,
                                "stream": True,
                                "content_character_count": content_character_count,
                                "chunk_id": chunk_id
                            },
                            request_data=request.model_dump()
                        )
                        yield translator.create_stream_done_chunk()
                        return
                else:
                    # Non-retryable error or max retries exceeded
                    if content_character_count < 2:
                        # Haven't started streaming much, can cancel cleanly
                        logger.warning("Non-retryable error before significant streaming, cancelling response")
                        save_crash_log(
                            operation="streaming_chat_completion_early_cancel",
                            error=e,
                            context={
                                "model": request.model,
                                "stream": True,
                                "content_character_count": content_character_count,
                                "chunk_id": chunk_id
                            },
                            request_data=request.model_dump()
                        )
                        return
                    else:
                        # Already streaming, end cleanly
                        logger.error(f"Non-retryable error after streaming {content_character_count} characters")
                        save_crash_log(
                            operation="streaming_chat_completion_non_retryable",
                            error=e,
                            context={
                                "model": request.model,
                                "stream": True,
                                "content_character_count": content_character_count,
                                "chunk_id": chunk_id
                            },
                            request_data=request.model_dump()
                        )
                        yield translator.create_stream_done_chunk()
                        return

    except Exception as e:
        # Final catch-all error handler
        logger.error(f"Unexpected error in streaming chat completion: {e}")
        save_crash_log(
            operation="streaming_chat_completion_unexpected",
            error=e,
            context={
                "model": request.model,
                "stream": True,
                "content_character_count": content_character_count,
                "chunk_id": chunk_id
            },
            request_data=request.model_dump()
        )

        # End stream cleanly regardless of state
        yield translator.create_stream_done_chunk()


@app.post("/v1/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest,
    authorization: str = Header(None),
    x_api_key: str = Header(None, alias="x-api-key")
):
    """
    Create a chat completion (OpenAI-compatible endpoint)

    This endpoint accepts OpenAI-formatted requests and translates them to ThinkBuddy API calls.
    """
    try:
        logger.info(f"Received chat completion request for model: {request.model}")
        logger.debug(f"Request: {request.model_dump()}")

        # Check API key
        api_key = check_api_key(authorization, x_api_key)
        logger.info(f"🔑 Valid API key provided: {api_key[:10]}...")

        # Validate request
        if not request.messages:
            create_error_response("Messages cannot be empty")

        # Handle streaming
        if request.stream:
            return StreamingResponse(
                stream_chat_completion(request),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"  # Disable nginx buffering
                }
            )

        # Handle non-streaming
        # Translate request
        thinkbuddy_request = translator.openai_to_thinkbuddy(request)

        # Call ThinkBuddy API
        thinkbuddy_response = await thinkbuddy_client.chat_completion(thinkbuddy_request)

        # Translate response
        openai_response = translator.thinkbuddy_to_openai(thinkbuddy_response, request)

        logger.info(f"Successfully processed chat completion request")
        return openai_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing chat completion: {e}")
        save_crash_log(
            operation="chat_completion",
            error=e,
            context={"model": request.model, "stream": request.stream},
            request_data=request.model_dump()
        )
        create_error_response(
            f"Internal server error: {str(e)}",
            error_type="internal_error",
            status_code=500
        )


if __name__ == "__main__":
    import uvicorn

    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,
        log_level=os.getenv("LOG_LEVEL", "info").lower()
    )
